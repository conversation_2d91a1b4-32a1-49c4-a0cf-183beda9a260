from contextlib import asynccontextmanager
from datetime import datetime

from fastapi import Fast<PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.openapi.utils import get_openapi

from app.api.v1 import api_router
from app.core.config import settings
from app.core.log_setup import setup_logging
from app.core.middleware import ExceptionMiddleware

# 定义API文档的基本信息
API_TITLE = settings.PROJECT_NAME + " API"
API_DESCRIPTION = settings.PROJECT_NAME + " Backend API Service"
API_VERSION = settings.VERSION


@asynccontextmanager
async def lifespan(_):
    # 这里的代码在应用启动时执行
    setup_logging()
    settings.start_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    yield
    # 这里的代码在应用关闭时执行


app = FastAPI(
    title=API_TITLE,
    description=API_DESCRIPTION,
    version=API_VERSION,
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan,
)


def custom_openapi():
    if app.openapi_schema:
        return app.openapi_schema

    openapi_schema = get_openapi(
        title=API_TITLE,
        version=API_VERSION,
        description=API_DESCRIPTION,
        routes=app.routes,
    )

    # 添加 JWT bearer 认证方案
    openapi_schema["components"]["securitySchemes"] = {
        "JWT": {
            "type": "http",
            "scheme": "bearer",
            "bearerFormat": "JWT",
            "description": "Enter JWT Bearer token",
        }
    }

    app.openapi_schema = openapi_schema
    return app.openapi_schema


app.openapi = custom_openapi

# 定义允许的源（开发环境用）
origins = [
    "http://localhost：5173",
]

app.add_middleware(
    CORSMiddleware,  # Type:ignore
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
    expose_headers=["*"],
)

app.add_middleware(ExceptionMiddleware)  # Type:ignore

app.include_router(api_router, prefix="/api/v1")
