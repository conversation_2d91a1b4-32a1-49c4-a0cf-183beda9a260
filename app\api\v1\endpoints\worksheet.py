from fastapi import APIRouter, Depends, HTTPException, Path, Query
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.models.models import TntStudent
from app.schemas.worksheet import (
    QuestionCommentRequest,
    QuestionRetryRequest,
    QuestionDraftBatchRequest,
    UnitQuestionsResponse,
    WorksheetBasicResponse,
    WorksheetUnitsResponse,
)
from app.services.ai import get_fastgpt_stream_response
from app.services.auth import get_current_user
from app.services.bot import get_bot_config
from app.core.constants import BOT_KEY_DDCMT
from app.services.worksheet import (
    batch_update_question_drafts,
    get_question_info_for_comment,
    get_unit_questions,
    get_worksheet_basic_info,
    get_worksheet_units,
    retry_question_answer,
    update_worksheet_answer_callback,
)

router = APIRouter()


@router.get("/{id}", response_model=WorksheetBasicResponse)
async def get_worksheet_api(
    id: int = Path(..., description="作业单ID"),
    class_id: int = Query(..., description="班级ID"),
    current_user: TntStudent = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """
    获取作业单基本信息

    ## 功能描述
    获取指定作业单的基本信息，包括练习状态、时间信息等，但不包含单元列表。

    ## 请求参数
    - **id** (int): 作业单ID，通过路径参数传递
    - **class_id** (int): 班级ID，通过查询参数传递

    ## 响应
    - **200**: 成功返回作业单基本信息
        - 返回类型: WorksheetBasicResponse
        - 包含作业单的基本信息：
            - title: 标题
            - pic: 图片（OSS签名URL或null）
            - intro: 简介
            - duration: 时长（分钟）
            - bgtext: 背景文字
            - bgvideo: 背景视频URL（OSS签名URL或null）
            - report: 整体点评报告URL（OSS签名URL或null）
            - btime: 开始练习时间
            - stime: 提交练习时间
            - utime: 上次练习时间
            - status: 练习状态（0：待练习；1：练习中；2：已提交）
            - eid: 练习ID
            - elid: 练习情况ID（可为null）
            - tid: 老师ID（可为null）
            - tname: 老师姓名（可为null）
            - tavatar: 老师头像URL（OSS签名URL或null）

    ## 权限要求
    - 需要有效的用户身份令牌
    - 只能查看当前租户下的作业单

    ## 业务规则
    - 只能访问当前租户下的作业单
    - 只能访问有效的（active=1）练习

    ## 错误处理
    - **404**: 作业单不存在或无权访问
        - 作业单ID不存在
        - 无权访问该作业单（不属于当前租户）
        - 关联的练习已被删除或无效
    """
    result = get_worksheet_basic_info(db, id, class_id, current_user)

    if not result:
        raise HTTPException(status_code=404, detail="作业单不存在")

    return result


@router.get("/{id}/units", response_model=WorksheetUnitsResponse)
async def get_worksheet_units_api(
    id: int = Path(..., description="作业单ID"),
    current_user: TntStudent = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """
    获取作业单单元列表

    ## 功能描述
    获取指定作业单的所有单元列表。

    ## 请求参数
    - **id** (int): 作业单ID，通过路径参数传递

    ## 响应
    - **200**: 成功返回单元列表
        - 返回类型: WorksheetUnitsResponse
        - 包含单元列表信息：
            - unit_list: 单元列表（按priority从小到大排序）
                - id: 单元ID
                - name: 单元名称
                - bgtext: 背景文字
                - bgvideo: 背景视频URL（OSS签名URL或null）

    ## 权限要求
    - 需要有效的用户身份令牌
    - 只能查看当前租户下的作业单

    ## 业务规则
    - 只能访问当前租户下的作业单
    - 单元按priority字段从小到大排序

    ## 错误处理
    - **404**: 作业单不存在或无权访问
        - 作业单ID不存在
        - 无权访问该作业单（不属于当前租户）
    """
    result = get_worksheet_units(db, id, current_user)

    if not result:
        raise HTTPException(status_code=404, detail="作业单不存在")

    return result


@router.get("/{wid}/unit/{uid}/questions", response_model=UnitQuestionsResponse)
async def get_unit_questions_api(
    wid: int = Path(..., description="作业单ID"),
    uid: int = Path(..., description="单元ID"),
    elid: int = Query(..., description="练习情况ID"),
    current_user: TntStudent = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """
    获取作业单中单元下的所有问题信息

    ## 功能描述
    获取指定作业单中指定单元下的所有问题信息，包括问题详情、理论框架、作答指南等。

    ## 请求参数
    - **wid** (int): 作业单ID，通过路径参数传递
    - **uid** (int): 单元ID，通过路径参数传递
    - **elid** (int): 练习情况ID，通过查询参数传递

    ## 响应
    - **200**: 成功返回单元问题列表
        - 返回类型: UnitQuestionsResponse
        - 包含问题的完整信息：
            - question_list: 问题列表
                - id: 问题ID
                - title: 问题标题
                - bgtext: 背景文字
                - bgvideo: 背景视频URL（OSS签名URL或null）
                - draft: 作答草稿
                - answer: 已提交作答
                - comment: AI点评
                - framework_list: 理论框架列表（按priority从小到大排序）
                    - name: 理论框架名称
                    - logo: 理论框架logo（OSS签名URL或null）
                    - module_list: 理论模块列表（按priority从小到大排序）
                        - name: 理论模块名称
                - guide_list: 作答指南列表（按priority从小到大排序）
                    - title: 指南标题
                    - details: 指南详情

    ## 权限要求
    - 需要有效的用户身份令牌
    - 只能查看当前租户下的作业单和单元

    ## 业务规则
    - 只能访问当前租户下的作业单和单元
    - 只能访问有效的（active=1）问题
    - 问题按worksheet_asm表中的priority字段从小到大排序
    - 理论框架按framework表中的priority字段从小到大排序
    - 理论模块按module表中的priority字段从小到大排序
    - 作答指南按question_guide表中的priority字段从小到大排序

    ## 错误处理
    - **404**: 作业单或单元不存在或无权访问
        - 作业单ID不存在
        - 单元ID不存在
        - 单元不属于指定的作业单
        - 无权访问该作业单或单元（不属于当前租户）
    """
    result = get_unit_questions(db, wid, uid, current_user, elid)

    if result is None:
        raise HTTPException(status_code=404, detail="作业单不存在")

    return result


@router.post("/question/comment")
async def get_question_comment_api(
    request: QuestionCommentRequest,
    current_user: TntStudent = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """
    获取AI老师对问题作答的流式点评

    ## 功能描述
    根据问题ID和学员作答内容，调用AI服务获取老师点评，并将结果保存到数据库。

    ## 请求参数
    - **request** (QuestionCommentRequest): 点评请求信息，请求体
        - elid: 练习情况ID
        - qid: 问题ID
        - answer: 作答内容

    ## 响应
    - **200**: 成功返回流式点评响应
        - 返回类型: StreamingResponse
        - 内容类型: text/event-stream
        - 流式返回AI点评内容

    ## 权限要求
    - 需要有效的用户身份令牌
    - 只能对当前租户下的问题进行点评

    ## 业务规则
    - 只能访问当前租户下的问题
    - 只能访问有效的（active=1）问题
    - 点评完成后自动保存到数据库

    ## 错误处理
    - **404**: 问题不存在或无权访问
        - 问题ID不存在
        - 无权访问该问题（不属于当前租户）
        - 问题已被删除或无效
    - **500**: 服务器内部错误
        - AI服务配置错误
        - 数据库操作失败
    """
    # Step 1: 获取问题信息
    question_info = get_question_info_for_comment(db, request.qid, current_user)
    if not question_info:
        raise HTTPException(status_code=404, detail="问题不存在")

    # 构造variables
    variables = {
        "question": question_info["bgtext"] or "",
        "skills": question_info["pv_skills"] or "",
        "rules": question_info["pv_rules"] or "",
        "format": question_info["pv_formats"] or "",
        "student": current_user.name,
    }

    # Step 2: 获取机器人配置
    bot_config = get_bot_config(db, current_user.tenant_id, BOT_KEY_DDCMT)
    if not bot_config:
        raise HTTPException(status_code=500, detail="AI服务配置错误")

    # Step 3: 构造回调函数
    async def on_completion_callback(full_content: str) -> None:
        await update_worksheet_answer_callback(
            db,
            current_user.tenant_id,
            request.elid,
            request.qid,
            request.answer,
            full_content,
        )
        pass

    # Step 4: 调用AI服务
    try:
        return await get_fastgpt_stream_response(
            messages=[request.answer],
            api_endpoint=bot_config["api_endpoint"],
            api_key=bot_config["api_key"],
            variables=variables,
            on_completion=on_completion_callback,
        )
    except Exception as e:
        # 捕获AI服务调用异常（包括超时、网络错误等）
        raise HTTPException(status_code=500, detail=f"AI服务调用失败: {str(e)}")


@router.post("/question/retry")
async def retry_question_api(
    request: QuestionRetryRequest,
    current_user: TntStudent = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """
    重新练习问题

    ## 功能描述
    根据练习情况ID和问题ID，将该问题的已提交答案重置为草稿状态，清空已提交答案和AI点评。

    ## 请求参数
    - **request** (QuestionRetryRequest): 重新练习请求信息，请求体
        - elid: 练习情况ID
        - qid: 问题ID

    ## 响应
    - **200**: 成功重置问题状态
        - 返回类型: dict
        - 内容: {"message": "重新练习成功"}

    ## 权限要求
    - 需要有效的用户身份令牌
    - 只能对当前租户下的问题进行重新练习

    ## 业务规则
    - 将问题的answer值复制到draft字段
    - 将answer和comment字段设为null
    - 只能操作当前租户下的记录

    ## 错误处理
    - **404**: 问题记录不存在或无权访问
        - 问题记录不存在
        - 无权访问该问题记录（不属于当前租户）
    - **500**: 服务器内部错误
        - 数据库操作失败
    """
    # 执行重新练习操作
    success = retry_question_answer(
        db, current_user.tenant_id, request.elid, request.qid
    )

    if not success:
        raise HTTPException(status_code=404, detail="问题记录不存在")

    return {"message": "重新练习成功"}


@router.put("/question/answers")
async def batch_update_question_drafts_api(
    request: QuestionDraftBatchRequest,
    current_user: TntStudent = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """
    批量更新问题作答草稿

    ## 功能描述
    批量更新指定练习情况下多个问题的作答草稿，并更新练习记录的最后更新时间。

    ## 请求参数
    - **request** (QuestionDraftBatchRequest): 批量更新请求信息，请求体
        - elid: 练习情况ID
        - list: 问题草稿列表
            - qid: 问题ID
            - draft: 草稿内容

    ## 响应
    - **200**: 成功批量更新草稿
        - 返回类型: dict
        - 内容: {"message": "批量更新成功"}

    ## 权限要求
    - 需要有效的用户身份令牌
    - 只能更新当前租户下的问题草稿

    ## 业务规则
    - 批量更新tnt_worksheet_answer表中的draft字段
    - 同时更新tnt_exercise_log表的utime字段为当前时间
    - 两个表的更新操作在同一事务中执行
    - 如果记录不存在则创建新记录
    - 只能操作当前租户下的记录

    ## 错误处理
    - **404**: 练习记录不存在或无权访问
        - 练习情况ID不存在
        - 无权访问该练习记录（不属于当前租户）
    - **500**: 服务器内部错误
        - 数据库操作失败
        - 事务回滚
    """
    success = batch_update_question_drafts(
        db=db,
        tenant_id=current_user.tenant_id,
        elid=request.elid,
        draft_items=request.list
    )

    if not success:
        raise HTTPException(status_code=404, detail="练习记录不存在")

    return {"message": "更新成功"}
