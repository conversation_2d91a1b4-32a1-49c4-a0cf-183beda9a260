<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TTS WebSocket 测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input, textarea, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .status {
            margin-top: 20px;
            padding: 10px;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .audio-player {
            margin-top: 20px;
            text-align: center;
        }
        audio {
            width: 100%;
            max-width: 400px;
        }
        .progress {
            margin-top: 10px;
            background-color: #e9ecef;
            border-radius: 5px;
            height: 20px;
            overflow: hidden;
        }
        .progress-bar {
            background-color: #007bff;
            height: 100%;
            width: 0%;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎤 TTS WebSocket 测试页面</h1>
        
        <form id="ttsForm">
            <div class="form-group">
                <label for="serverUrl">服务器地址:</label>
                <input type="text" id="serverUrl" value="ws://localhost:8000/api/v1/tts/ws" required>
            </div>
            
            <div class="form-group">
                <label for="text">要合成的文本:</label>
                <textarea id="text" placeholder="请输入要合成语音的文本内容..." required>你好，这是一个语音合成测试。欢迎使用TTS WebSocket接口！</textarea>
            </div>
            
            <div class="form-group">
                <label for="timbre">音色:</label>
                <select id="timbre" required>
                    <option value="BV001_streaming">BV001_streaming (女声)</option>
                    <option value="BV002_streaming">BV002_streaming (男声)</option>
                    <option value="BV003_streaming">BV003_streaming (女声)</option>
                    <option value="BV004_streaming">BV004_streaming (男声)</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="speedRatio">语速 (0.5-2.0):</label>
                <input type="number" id="speedRatio" min="0.5" max="2.0" step="0.1" value="1.0">
            </div>
            
            <div class="form-group">
                <label for="volumeRatio">音量 (0.5-2.0):</label>
                <input type="number" id="volumeRatio" min="0.5" max="2.0" step="0.1" value="1.0">
            </div>
            
            <div class="form-group">
                <label for="pitchRatio">音调 (0.5-2.0):</label>
                <input type="number" id="pitchRatio" min="0.5" max="2.0" step="0.1" value="1.0">
            </div>
            
            <button type="submit" id="startBtn">🎵 开始合成</button>
            <button type="button" id="stopBtn" disabled>⏹️ 停止</button>
        </form>
        
        <div class="progress" id="progressContainer" style="display: none;">
            <div class="progress-bar" id="progressBar"></div>
        </div>
        
        <div id="status"></div>
        
        <div class="audio-player" id="audioPlayer" style="display: none;">
            <h3>🔊 合成结果:</h3>
            <audio id="audioElement" controls></audio>
            <br><br>
            <button type="button" id="downloadBtn">💾 下载音频</button>
        </div>
    </div>

    <script>
        let websocket = null;
        let audioChunks = [];
        let isConnecting = false;

        const form = document.getElementById('ttsForm');
        const startBtn = document.getElementById('startBtn');
        const stopBtn = document.getElementById('stopBtn');
        const status = document.getElementById('status');
        const audioPlayer = document.getElementById('audioPlayer');
        const audioElement = document.getElementById('audioElement');
        const downloadBtn = document.getElementById('downloadBtn');
        const progressContainer = document.getElementById('progressContainer');
        const progressBar = document.getElementById('progressBar');

        function showStatus(message, type = 'info') {
            status.innerHTML = message;
            status.className = `status ${type}`;
        }

        function updateProgress(percent) {
            progressBar.style.width = `${percent}%`;
        }

        function resetUI() {
            startBtn.disabled = false;
            stopBtn.disabled = true;
            progressContainer.style.display = 'none';
            updateProgress(0);
        }

        function startTTS() {
            if (isConnecting) return;
            
            const serverUrl = document.getElementById('serverUrl').value;
            const text = document.getElementById('text').value;
            const timbre = document.getElementById('timbre').value;
            const speedRatio = parseFloat(document.getElementById('speedRatio').value);
            const volumeRatio = parseFloat(document.getElementById('volumeRatio').value);
            const pitchRatio = parseFloat(document.getElementById('pitchRatio').value);

            if (!text.trim()) {
                showStatus('请输入要合成的文本', 'error');
                return;
            }

            isConnecting = true;
            startBtn.disabled = true;
            stopBtn.disabled = false;
            audioChunks = [];
            audioPlayer.style.display = 'none';
            progressContainer.style.display = 'block';
            updateProgress(10);

            showStatus('正在连接到服务器...', 'info');

            try {
                websocket = new WebSocket(serverUrl);

                websocket.onopen = function() {
                    showStatus('连接成功，发送TTS请求...', 'info');
                    updateProgress(20);
                    
                    const request = {
                        text: text,
                        timbre: timbre,
                        speed_ratio: speedRatio,
                        volume_ratio: volumeRatio,
                        pitch_ratio: pitchRatio,
                        encoding: 'mp3'
                    };

                    websocket.send(JSON.stringify(request));
                };

                websocket.onmessage = function(event) {
                    if (typeof event.data === 'string') {
                        // JSON响应
                        const response = JSON.parse(event.data);
                        console.log('收到JSON响应:', response);
                        
                        if (response.success) {
                            if (response.message.includes('completed')) {
                                showStatus('语音合成完成！', 'success');
                                updateProgress(100);
                                playAudio();
                            } else {
                                showStatus(response.message, 'info');
                                updateProgress(30);
                            }
                        } else {
                            showStatus(`错误: ${response.error_message}`, 'error');
                            resetUI();
                        }
                    } else {
                        // 音频数据
                        audioChunks.push(event.data);
                        console.log(`收到音频数据块: ${event.data.size} bytes`);
                        
                        // 更新进度
                        const progress = Math.min(30 + (audioChunks.length * 5), 90);
                        updateProgress(progress);
                        showStatus(`正在接收音频数据... (${audioChunks.length} 块)`, 'info');
                    }
                };

                websocket.onclose = function(event) {
                    console.log('WebSocket连接已关闭:', event);
                    isConnecting = false;
                    resetUI();
                    
                    if (event.code !== 1000) {
                        showStatus(`连接关闭 (代码: ${event.code})`, 'error');
                    }
                };

                websocket.onerror = function(error) {
                    console.error('WebSocket错误:', error);
                    showStatus('WebSocket连接错误', 'error');
                    isConnecting = false;
                    resetUI();
                };

            } catch (error) {
                console.error('连接失败:', error);
                showStatus(`连接失败: ${error.message}`, 'error');
                isConnecting = false;
                resetUI();
            }
        }

        function stopTTS() {
            if (websocket) {
                websocket.close();
                websocket = null;
            }
            showStatus('已停止语音合成', 'info');
            isConnecting = false;
            resetUI();
        }

        function playAudio() {
            if (audioChunks.length === 0) {
                showStatus('没有音频数据可播放', 'error');
                return;
            }

            const audioBlob = new Blob(audioChunks, { type: 'audio/mpeg' });
            const audioUrl = URL.createObjectURL(audioBlob);
            
            audioElement.src = audioUrl;
            audioPlayer.style.display = 'block';
            
            // 设置下载功能
            downloadBtn.onclick = function() {
                const a = document.createElement('a');
                a.href = audioUrl;
                a.download = `tts_audio_${Date.now()}.mp3`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
            };
        }

        // 事件监听
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            startTTS();
        });

        stopBtn.addEventListener('click', stopTTS);

        // 页面加载完成后显示初始状态
        showStatus('请填写参数并点击"开始合成"按钮', 'info');
    </script>
</body>
</html>
