import json
import gzip
import uuid
import logging
from typing import AsyncGenerator, Optional
import websockets

from app.core.config import settings

logger = logging.getLogger(__name__)

# 消息类型定义
MESSAGE_TYPES = {
    11: "audio-only server response", 
    12: "frontend server response", 
    15: "error message from server"
}

MESSAGE_TYPE_SPECIFIC_FLAGS = {
    0: "no sequence number", 
    1: "sequence number > 0",
    2: "last message from server (seq < 0)", 
    3: "sequence number < 0"
}

MESSAGE_SERIALIZATION_METHODS = {
    0: "no serialization", 
    1: "JSON", 
    15: "custom type"
}

MESSAGE_COMPRESSIONS = {
    0: "no compression", 
    1: "gzip", 
    15: "custom compression method"
}

# 默认请求头
# version: b0001 (4 bits)
# header size: b0001 (4 bits)  
# message type: b0001 (Full client request) (4bits)
# message type specific flags: b0000 (none) (4bits)
# message serialization method: b0001 (JSON) (4 bits)
# message compression: b0001 (gzip) (4bits)
# reserved data: 0x00 (1 byte)
DEFAULT_HEADER = bytearray(b'\x11\x10\x11\x00')


def create_tts_request_json(text: str, timbre: str, speed_ratio: float = 1.0, 
                           volume_ratio: float = 1.0, pitch_ratio: float = 1.0,
                           encoding: str = "mp3") -> dict:
    """创建TTS请求JSON"""
    request_json = {
        "app": {
            "appid": settings.TTS_APP_ID,
            "token": "access_token",
            "cluster": settings.TTS_CLUSTER
        },
        "user": {
            "uid": "388808087185088"
        },
        "audio": {
            "voice_type": timbre,
            "encoding": encoding,
            "speed_ratio": speed_ratio,
            "volume_ratio": volume_ratio,
            "pitch_ratio": pitch_ratio,
        },
        "request": {
            "reqid": str(uuid.uuid4()),
            "text": text,
            "text_type": "plain",
            "operation": "submit"
        }
    }
    return request_json


def create_binary_request(request_json: dict) -> bytearray:
    """创建二进制请求数据"""
    payload_bytes = str.encode(json.dumps(request_json))
    payload_bytes = gzip.compress(payload_bytes)
    
    full_client_request = bytearray(DEFAULT_HEADER)
    full_client_request.extend((len(payload_bytes)).to_bytes(4, 'big'))
    full_client_request.extend(payload_bytes)
    
    return full_client_request


def parse_response(res: bytes) -> tuple[bool, Optional[bytes], Optional[dict]]:
    """
    解析火山引擎TTS响应
    
    Returns:
        tuple: (is_done, audio_data, error_info)
        - is_done: 是否完成
        - audio_data: 音频数据（如果有）
        - error_info: 错误信息（如果有）
    """
    try:
        protocol_version = res[0] >> 4
        header_size = res[0] & 0x0f
        message_type = res[1] >> 4
        message_type_specific_flags = res[1] & 0x0f
        serialization_method = res[2] >> 4
        message_compression = res[2] & 0x0f
        reserved = res[3]
        header_extensions = res[4:header_size*4]
        payload = res[header_size*4:]
        
        logger.debug(f"Message type: {message_type}, flags: {message_type_specific_flags}")
        
        if message_type == 0xb:  # audio-only server response
            if message_type_specific_flags == 0:  # no sequence number as ACK
                return False, None, None
            else:
                sequence_number = int.from_bytes(payload[:4], "big", signed=True)
                payload_size = int.from_bytes(payload[4:8], "big", signed=False)
                audio_data = payload[8:]
                
                logger.debug(f"Sequence number: {sequence_number}, payload size: {payload_size}")
                
                is_done = sequence_number < 0
                return is_done, audio_data, None
                
        elif message_type == 0xf:  # error message
            code = int.from_bytes(payload[:4], "big", signed=False)
            msg_size = int.from_bytes(payload[4:8], "big", signed=False)
            error_msg = payload[8:]
            if message_compression == 1:
                error_msg = gzip.decompress(error_msg)
            error_msg = str(error_msg, "utf-8")
            
            error_info = {
                "error_code": code,
                "error_message": error_msg
            }
            return True, None, error_info
            
        elif message_type == 0xc:  # frontend server response
            msg_size = int.from_bytes(payload[:4], "big", signed=False)
            payload = payload[4:]
            if message_compression == 1:
                payload = gzip.decompress(payload)
            logger.debug(f"Frontend message: {payload}")
            return False, None, None
            
        else:
            logger.warning(f"Undefined message type: {message_type}")
            return True, None, {"error_code": -1, "error_message": "Undefined message type"}
            
    except Exception as e:
        logger.error(f"Error parsing response: {str(e)}")
        return True, None, {"error_code": -1, "error_message": f"Parse error: {str(e)}"}


async def tts_websocket_stream(text: str, timbre: str, speed_ratio: float = 1.0,
                              volume_ratio: float = 1.0, pitch_ratio: float = 1.0,
                              encoding: str = "mp3") -> AsyncGenerator[bytes, None]:
    """
    TTS WebSocket流式处理
    
    Args:
        text: 要合成的文本
        timbre: 音色
        speed_ratio: 语速比例
        volume_ratio: 音量比例  
        pitch_ratio: 音调比例
        encoding: 音频编码格式
        
    Yields:
        bytes: 音频数据块
        
    Raises:
        Exception: 当连接失败或处理错误时
    """
    api_url = f"wss://{settings.TTS_HOST}/api/v1/tts/ws_binary"
    headers = {"Authorization": f"Bearer; {settings.TTS_ACCESS_TOKEN}"}
    
    request_json = create_tts_request_json(
        text=text,
        timbre=timbre, 
        speed_ratio=speed_ratio,
        volume_ratio=volume_ratio,
        pitch_ratio=pitch_ratio,
        encoding=encoding
    )
    
    binary_request = create_binary_request(request_json)
    
    logger.info(f"Connecting to TTS WebSocket: {api_url}")
    logger.debug(f"Request JSON: {request_json}")
    
    try:
        async with websockets.connect(api_url, extra_headers=headers, ping_interval=None) as ws:
            await ws.send(binary_request)
            
            while True:
                res = await ws.recv()
                is_done, audio_data, error_info = parse_response(res)
                
                if error_info:
                    logger.error(f"TTS error: {error_info}")
                    raise Exception(f"TTS error: {error_info['error_message']}")
                
                if audio_data:
                    yield audio_data
                
                if is_done:
                    logger.info("TTS stream completed")
                    break
                    
    except websockets.exceptions.WebSocketException as e:
        logger.error(f"WebSocket connection error: {str(e)}")
        raise Exception(f"WebSocket connection failed: {str(e)}")
    except Exception as e:
        logger.error(f"TTS stream error: {str(e)}")
        raise
