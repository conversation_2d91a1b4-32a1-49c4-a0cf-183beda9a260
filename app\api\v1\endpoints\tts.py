import logging

from fastapi import APIRouter, WebSocket, WebSocketDisconnect, HTTPException, status
from fastapi.responses import StreamingResponse

from app.schemas.tts import TTSWebSocketRequest, TTSWebSocketResponse, TTSErrorResponse
from app.services.tts import tts_websocket_stream

logger = logging.getLogger(__name__)

router = APIRouter()


@router.websocket("/ws")
async def tts_websocket_endpoint(websocket: WebSocket):
    """
    TTS WebSocket端点
    
    ## 功能描述
    提供流式语音合成服务，客户端通过WebSocket连接发送文本，服务器返回合成的音频流。
    
    ## 连接流程
    1. 客户端建立WebSocket连接
    2. 发送JSON格式的TTS请求
    3. 服务器返回音频数据流
    4. 连接结束
    
    ## 请求格式
    ```json
    {
        "text": "要合成的文本内容",
        "timbre": "音色类型",
        "speed_ratio": 1.0,
        "volume_ratio": 1.0, 
        "pitch_ratio": 1.0,
        "encoding": "mp3"
    }
    ```
    
    ## 响应格式
    - 成功时：直接返回音频二进制数据流
    - 错误时：返回JSON格式的错误信息
    
    ## 错误处理
    - 连接断开：自动清理资源
    - 参数错误：返回错误信息并关闭连接
    - TTS服务错误：返回错误信息并关闭连接
    """
    await websocket.accept()
    logger.info("TTS WebSocket connection established")
    
    try:
        # 接收客户端请求
        data = await websocket.receive_json()
        logger.debug(f"Received TTS request: {data}")
        
        # 验证请求数据
        try:
            request = TTSWebSocketRequest(**data)
        except Exception as e:
            error_response = TTSErrorResponse(
                error_code=400,
                error_message=f"Invalid request format: {str(e)}"
            )
            await websocket.send_json(error_response.model_dump())
            await websocket.close(code=1003)
            return
        
        # 发送确认响应
        response = TTSWebSocketResponse(
            success=True,
            message="Request received, starting TTS processing",
            request_id=None
        )
        await websocket.send_json(response.model_dump())
        
        # 开始TTS流式处理
        try:
            async for audio_chunk in tts_websocket_stream(
                text=request.text,
                timbre=request.timbre,
                speed_ratio=request.speed_ratio or 1.0,
                volume_ratio=request.volume_ratio or 1.0,
                pitch_ratio=request.pitch_ratio or 1.0,
                encoding=request.encoding or "mp3"
            ):
                # 发送音频数据
                await websocket.send_bytes(audio_chunk)
                
        except Exception as e:
            logger.error(f"TTS processing error: {str(e)}")
            error_response = TTSErrorResponse(
                error_code=500,
                error_message=f"TTS processing failed: {str(e)}"
            )
            await websocket.send_json(error_response.model_dump())
            await websocket.close(code=1011)
            return
        
        # 发送完成响应
        completion_response = TTSWebSocketResponse(
            success=True,
            message="TTS processing completed",
            request_id=None
        )
        await websocket.send_json(completion_response.model_dump())
        
        logger.info("TTS WebSocket processing completed successfully")
        
    except WebSocketDisconnect:
        logger.info("TTS WebSocket client disconnected")
    except Exception as e:
        logger.error(f"TTS WebSocket error: {str(e)}")
        try:
            error_response = TTSErrorResponse(
                error_code=500,
                error_message=f"Internal server error: {str(e)}"
            )
            await websocket.send_json(error_response.model_dump())
            await websocket.close(code=1011)
        except:
            pass  # 连接可能已经关闭
    finally:
        logger.info("TTS WebSocket connection closed")


@router.post("/stream")
async def tts_stream_endpoint(request: TTSWebSocketRequest) -> StreamingResponse:
    """
    TTS流式HTTP端点（备用方案）
    
    ## 功能描述
    提供基于HTTP的流式语音合成服务，作为WebSocket的备用方案。
    
    ## 请求参数
    - **text** (str): 要合成的文本内容
    - **timbre** (str): 音色类型
    - **speed_ratio** (float, optional): 语速比例，默认1.0
    - **volume_ratio** (float, optional): 音量比例，默认1.0
    - **pitch_ratio** (float, optional): 音调比例，默认1.0
    - **encoding** (str, optional): 音频编码格式，默认mp3
    
    ## 响应
    - **200**: 成功返回音频流
        - 返回类型: StreamingResponse
        - 内容类型: audio/mpeg (mp3) 或其他音频格式
    
    ## 错误处理
    - **400**: 请求参数错误
    - **500**: TTS服务错误
    """
    try:
        async def generate_audio():
            async for audio_chunk in tts_websocket_stream(
                text=request.text,
                timbre=request.timbre,
                speed_ratio=request.speed_ratio or 1.0,
                volume_ratio=request.volume_ratio or 1.0,
                pitch_ratio=request.pitch_ratio or 1.0,
                encoding=request.encoding or "mp3"
            ):
                yield audio_chunk
        
        media_type = "audio/mpeg" if request.encoding == "mp3" else f"audio/{request.encoding}"
        
        return StreamingResponse(
            generate_audio(),
            media_type=media_type,
            headers={
                "Content-Disposition": "attachment; filename=tts_audio.mp3",
                "Cache-Control": "no-cache"
            }
        )
        
    except Exception as e:
        logger.error(f"TTS stream error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"TTS processing failed: {str(e)}"
        )
