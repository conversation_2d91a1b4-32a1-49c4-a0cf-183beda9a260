#!/usr/bin/env python3
"""
TTS WebSocket服务器启动脚本

使用方法:
1. 确保已安装所有依赖: pip install -r requirements.txt
2. 配置环境变量 (.env文件)
3. 运行: python start_server.py

注意: 这是一个开发用的启动脚本，生产环境请使用 uvicorn 或其他WSGI服务器
"""

import os
import sys
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def check_environment():
    """检查环境配置"""
    required_vars = [
        'TTS_APP_ID',
        'TTS_ACCESS_TOKEN', 
        'TTS_CLUSTER',
        'DATABASE_URL',
        'SECRET_KEY'
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print("❌ 缺少以下环境变量:")
        for var in missing_vars:
            print(f"   - {var}")
        print("\n请检查 .env 文件配置")
        return False
    
    print("✅ 环境变量检查通过")
    return True

def check_dependencies():
    """检查依赖包"""
    required_packages = [
        'fastapi',
        'uvicorn', 
        'websockets',
        'pydantic',
        'sqlalchemy'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ 缺少以下依赖包:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n请运行: pip install -r requirements.txt")
        return False
    
    print("✅ 依赖包检查通过")
    return True

def main():
    """主函数"""
    print("🚀 启动TTS WebSocket服务器")
    print("=" * 50)
    
    # 检查环境
    if not check_dependencies():
        sys.exit(1)
    
    if not check_environment():
        sys.exit(1)
    
    # 导入应用
    try:
        from main import app
        print("✅ FastAPI应用加载成功")
    except Exception as e:
        print(f"❌ FastAPI应用加载失败: {e}")
        sys.exit(1)
    
    # 启动服务器
    try:
        import uvicorn
        
        print("\n🌐 服务器信息:")
        print(f"   - 地址: http://localhost:8000")
        print(f"   - API文档: http://localhost:8000/docs")
        print(f"   - TTS WebSocket: ws://localhost:8000/api/v1/tts/ws")
        print(f"   - TTS HTTP流式: http://localhost:8000/api/v1/tts/stream")
        print(f"   - 测试页面: 打开 tts_websocket_demo.html")
        
        print("\n📝 测试方法:")
        print("   1. 打开浏览器访问 tts_websocket_demo.html")
        print("   2. 运行测试脚本: python test_tts_websocket.py")
        print("   3. 使用API文档测试: http://localhost:8000/docs")
        
        print("\n🔧 配置检查:")
        from app.core.config import settings
        print(f"   - TTS_APP_ID: {settings.TTS_APP_ID[:8]}...")
        print(f"   - TTS_CLUSTER: {settings.TTS_CLUSTER}")
        print(f"   - TTS_HOST: {settings.TTS_HOST}")
        
        print("\n" + "=" * 50)
        print("🎵 TTS WebSocket服务器启动中...")
        print("按 Ctrl+C 停止服务器")
        print("=" * 50)
        
        # 启动服务器
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=8000,
            reload=True,
            log_level="info"
        )
        
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
