import json
import logging
import uuid
from typing import AsyncGenerator, Awaitable, Callable, Optional

import httpx
from fastapi.responses import StreamingResponse

logger = logging.getLogger(__name__)


async def get_fastgpt_json_response(
    messages: list[str],
    api_endpoint: str,
    api_key: str,
    variables: dict[str, str] = {},
    chat_id: Optional[str] = None,
) -> str:
    """获取AI聊天完成响应（非流式，返回完整JSON字符串）

    Args:
        messages: 消息列表
        api_endpoint: API端点URL
        api_key: API密钥
        variables: 变量字典，默认为空
        chat_id: 聊天ID，默认为None

    Returns:
        完整的响应内容字符串

    Raises:
        Exception: 当API调用失败或响应格式错误时
    """
    if not messages:
        raise ValueError("消息列表不能为空")
    
    if not api_endpoint or not api_key:
        raise ValueError("API端点和密钥不能为空")

    # 设置超时参数
    timeout = httpx.Timeout(
        connect=10.0,  # 连接超时10秒
        read=120.0,  # 读取超时120秒（2分钟）
        write=10.0,  # 写入超时10秒
        pool=10.0,  # 连接池超时10秒
    )

    request_chat_id = str(uuid.uuid4()) if chat_id is None else chat_id
    logger.info(f"开始AI非流式请求，chat_id: {request_chat_id}, messages_count: {len(messages)}")

    try:
        async with httpx.AsyncClient(timeout=timeout) as client:
            # 构造消息数组
            messages_array = []
            for i, message in enumerate(messages):
                # 假设奇数索引为用户消息，偶数索引为助手消息
                role = "user" if i % 2 == 0 else "assistant"
                messages_array.append({"role": role, "content": message})

            # 构造请求体
            request_body = {
                "messages": messages_array,
                "stream": False,  # 非流式响应
                "detail": True,
                "chatId": request_chat_id,
                "variables": variables,
            }

            logger.debug(f"AI请求体: {json.dumps(request_body, ensure_ascii=False)}")

            response = await client.post(
                f"{api_endpoint}/chat/completions",
                headers={
                    "Authorization": f"Bearer {api_key}",
                    "Content-Type": "application/json",
                },
                json=request_body,
                timeout=timeout,
            )

            if response.status_code != 200:
                error_msg = f"AI API调用失败: {response.status_code} - {response.text}"
                logger.error(error_msg)
                raise Exception(error_msg)

            response_data = response.json()
            logger.debug(f"AI响应数据: {json.dumps(response_data, ensure_ascii=False)}")
            
            # 验证响应格式并提取内容
            if not isinstance(response_data, dict):
                raise Exception("AI API响应格式错误：响应不是有效的JSON对象")
            
            if "choices" not in response_data:
                raise Exception("AI API响应格式错误：缺少choices字段")
            
            choices = response_data["choices"]
            if not isinstance(choices, list) or len(choices) == 0:
                raise Exception("AI API响应格式错误：choices字段为空或格式错误")
            
            first_choice = choices[0]
            if not isinstance(first_choice, dict):
                raise Exception("AI API响应格式错误：choice格式错误")
            
            message = first_choice.get("message", {})
            if not isinstance(message, dict):
                raise Exception("AI API响应格式错误：message字段格式错误")
            
            content = message.get("content", "")
            if not isinstance(content, str):
                raise Exception("AI API响应格式错误：content字段不是字符串")
            
            logger.info(f"AI请求成功，chat_id: {request_chat_id}, content_length: {len(content)}")
            return content

    except httpx.TimeoutException as e:
        error_msg = f"AI API请求超时: {str(e)}"
        logger.error(error_msg)
        raise Exception(error_msg)
    except httpx.RequestError as e:
        error_msg = f"AI API网络请求错误: {str(e)}"
        logger.error(error_msg)
        raise Exception(error_msg)
    except json.JSONDecodeError as e:
        error_msg = f"AI API响应JSON解析错误: {str(e)}"
        logger.error(error_msg)
        raise Exception(error_msg)
    except Exception as e:
        if "AI API" not in str(e):
            logger.error(f"AI请求未知错误: {str(e)}")
        raise


async def get_fastgpt_stream_response(
    messages: list[str],
    api_endpoint: str,
    api_key: str,
    variables: dict[str, str] = {},
    chat_id: Optional[str] = None,
    on_completion: Optional[Callable[[str], Awaitable[None]]] = None,
) -> StreamingResponse:
    """获取AI聊天完成响应

    Args:
        messages: 消息列表
        api_endpoint: API端点URL
        api_key: API密钥
        variables: 变量字典，默认为空
        chat_id: 聊天ID，默认为None
        on_completion: 可选的回调函数，在成功处理完毕后调用，传入完整的响应内容

    Returns:
        流式响应对象
    """

    async def stream_response() -> AsyncGenerator[bytes, None]:
        full_content = ""
        save_message = False

        # 设置超时参数
        timeout = httpx.Timeout(
            connect=10.0,  # 连接超时10秒
            read=120.0,  # 读取超时120秒（2分钟）
            write=10.0,  # 写入超时10秒
            pool=10.0,  # 连接池超时10秒
        )

        async with httpx.AsyncClient(timeout=timeout) as client:
            try:
                # 构造消息数组
                messages_array = []
                for i, message in enumerate(messages):
                    # 假设奇数索引为用户消息，偶数索引为助手消息
                    role = "user" if i % 2 == 0 else "assistant"
                    messages_array.append({"role": role, "content": message})

                # 如果chat_id为None，生成一个新的UUID
                request_body = {
                    "messages": messages_array,
                    "stream": True,
                    "detail": True,
                    "chatId": str(uuid.uuid4()) if chat_id is None else chat_id,
                    "variables": variables,
                }

                async with client.stream(
                    "POST",
                    f"{api_endpoint}/chat/completions",
                    headers={
                        "Authorization": f"Bearer {api_key}",
                        "Content-Type": "application/json",
                    },
                    json=request_body,
                    timeout=timeout,
                ) as response:
                    if response.status_code != 200:
                        error = await response.aread()
                        yield f"event: error\ndata: {error.decode()}\n\n".encode()
                        return

                    async for line in response.aiter_lines():
                        if not line.startswith("data: "):
                            continue

                        raw_data = line[6:].strip()

                        if raw_data == "[DONE]":
                            save_message = True
                            yield b"event: answer\ndata: [DONE]\n\n"
                            break

                        try:
                            json_data = json.loads(raw_data)
                            if isinstance(json_data, dict) and "choices" in json_data:
                                delta = json_data["choices"][0].get("delta", {})
                                content = delta.get("content")
                                finish_reason = json_data["choices"][0].get(
                                    "finish_reason"
                                )

                                # 如果有内容，添加到full_content
                                if content:
                                    full_content += content

                                # 构造响应数据
                                response_data = {
                                    "id": "",
                                    "object": "",
                                    "created": 0,
                                    "model": "",
                                    "choices": [
                                        {
                                            "delta": delta,
                                            "index": 0,
                                            "finish_reason": finish_reason,
                                        }
                                    ],
                                }

                                # 发送响应，设置 ensure_ascii=False
                                yield f"event: answer\ndata: {json.dumps(response_data, ensure_ascii=False)}\n\n".encode()

                        except json.JSONDecodeError:
                            continue

            except httpx.TimeoutException as e:
                yield f"event: error\ndata: {json.dumps({'error': f'请求超时: {str(e)}'}, ensure_ascii=False)}\n\n".encode()
            except httpx.RequestError as e:
                yield f"event: error\ndata: {json.dumps({'error': f'网络请求错误: {str(e)}'}, ensure_ascii=False)}\n\n".encode()
            except Exception as e:
                yield f"event: error\ndata: {json.dumps({'error': f'未知错误: {str(e)}'}, ensure_ascii=False)}\n\n".encode()
            finally:
                # 如果处理成功且有回调函数，则调用回调函数
                if save_message and full_content and on_completion:
                    await on_completion(full_content)

    return StreamingResponse(stream_response(), media_type="text/event-stream")
