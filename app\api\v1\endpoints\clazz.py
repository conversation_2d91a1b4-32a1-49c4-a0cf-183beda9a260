from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.models.models import TntStudent
from app.schemas.clazz import ClassListResponse, ClassExerciseListResponse
from app.services.auth import get_current_user
from app.services.clazz import get_student_classes, get_class_exercises, get_class_name

router = APIRouter()


@router.get("/classes", response_model=ClassListResponse)
def get_classes_api(current_user: TntStudent = Depends(get_current_user), db: Session = Depends(get_db)):
    """
    获取当前用户所在班级列表

    ## 功能描述
    获取当前登录用户（学员）所在的所有班级，按优先级从小到大排序，如果优先级相同按开始时间倒序排列。

    ## 请求参数
    - 无需额外参数，通过Authorization header传递Bearer token

    ## 响应
    - **200**: 成功返回班级列表
        - 返回类型: ClassListResponse
        - 包含班级列表信息
            - classes: 班级列表
                - id: 班级ID
                - name: 班级名称
                - pic: 班级图片URL（OSS签名URL）
                - description: 班级描述
                - btime: 开始时间
                - etime: 结束时间

    ## 权限要求
    - 需要有效的用户身份令牌

    ## 错误处理
    - **401**: 未授权访问，当令牌无效或过期时返回此错误
    """
    # 获取当前用户所在的班级列表
    classes = get_student_classes(db, current_user.id, current_user.tenant_id)
    
    # 构造响应对象
    return ClassListResponse(classes=classes)


@router.get("/{id}/exercises", response_model=ClassExerciseListResponse)
def get_class_exercises_api(id: int, current_user: TntStudent = Depends(get_current_user), db: Session = Depends(get_db)):
    """
    获取指定班级的所有练习信息

    ## 功能描述
    获取指定班级的所有练习信息，包括练习详情和对应的老师信息，按优先级从小到大排序，如果优先级相同按创建时间倒序排列。

    ## 请求参数
    - **id** (int): 班级ID，路径参数
    - 通过Authorization header传递Bearer token进行身份验证

    ## 响应
    - **200**: 成功返回练习列表
        - 返回类型: ClassExerciseListResponse
        - 包含练习列表信息
            - class_name: 班级名称
            - exercises: 练习列表
                - e_id: 练习ID
                - e_title: 练习标题
                - e_type: 练习类型（1：作业单；2：角色扮演）
                - e_pic: 练习图片URL（OSS签名URL）
                - e_intro: 练习简介
                - e_duration: 练习时长（分钟）
                - t_id: 老师ID
                - t_name: 老师姓名
                - t_avatar: 老师头像URL（OSS签名URL）
                - t_intro: 老师简介
                - depend: 练习依赖（0：不依赖；1：依赖）
                - w_id: 作业单ID（如果存在）
                - s_id: 场景ID（如果存在）
                - el_id: 练习情况ID（如果存在）
                - el_status: 练习状态（0：待练习；1：练习中；2：已提交；如果为null表示无练习记录“未开始”）
                - el_btime: 开始练习时间
                - el_stime: 提交练习时间
                - el_utime: 上次更新时间

    ## 权限要求
    - 需要有效的用户身份令牌

    ## 错误处理
    - **401**: 未授权访问，当令牌无效或过期时返回此错误
    - **404**: 班级不存在或用户无权访问该班级
    """
    # 获取班级名称
    class_name = get_class_name(db, id, current_user.tenant_id)
    
    # 获取指定班级的练习列表
    exercises = get_class_exercises(db, id, current_user.tenant_id, current_user.id)
    
    # 构造响应对象
    return ClassExerciseListResponse(class_name=class_name, exercises=exercises)